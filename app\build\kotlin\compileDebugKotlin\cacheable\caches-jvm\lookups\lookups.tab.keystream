  Activity android.app  Application android.app  OTPAutoReadTheme android.app.Activity  SimpleOTPScreen android.app.Activity  enableEdgeToEdge android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  BroadcastReceiver android.content  Context android.content  ContextWrapper android.content  Intent android.content  IntentFilter android.content  Bundle !android.content.BroadcastReceiver  CommonStatusCodes !android.content.BroadcastReceiver  	Exception !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  Regex !android.content.BroadcastReceiver  RegexOption !android.content.BroadcastReceiver  SmsRetriever !android.content.BroadcastReceiver  Status !android.content.BroadcastReceiver  TAG !android.content.BroadcastReceiver  
isNotEmpty !android.content.BroadcastReceiver  
isNullOrEmpty !android.content.BroadcastReceiver  listOf !android.content.BroadcastReceiver  matches !android.content.BroadcastReceiver  OTPAutoReadTheme android.content.Context  SimpleOTPScreen android.content.Context  enableEdgeToEdge android.content.Context  packageManager android.content.Context  packageName android.content.Context  registerReceiver android.content.Context  
setContent android.content.Context  unregisterReceiver android.content.Context  OTPAutoReadTheme android.content.ContextWrapper  SimpleOTPScreen android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  
setContent android.content.ContextWrapper  action android.content.Intent  extras android.content.Intent  ApplicationInfo android.content.pm  PackageInfo android.content.pm  	Signature android.content.pm  packageName "android.content.pm.ApplicationInfo  
signatures android.content.pm.PackageInfo  packageName "android.content.pm.PackageItemInfo  GET_SIGNATURES !android.content.pm.PackageManager  getApplicationInfo !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  let android.content.pm.Signature  
toCharsString android.content.pm.Signature  Build 
android.os  Bundle 
android.os  get android.os.BaseBundle  	getString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  get android.os.Bundle  	getString android.os.Bundle  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  OTPAutoReadTheme  android.view.ContextThemeWrapper  SimpleOTPScreen  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  OTPAutoReadTheme #androidx.activity.ComponentActivity  SimpleOTPScreen #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  OTPAutoReadTheme -androidx.activity.ComponentActivity.Companion  SimpleOTPScreen -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  RequiresApi androidx.annotation  
background androidx.compose.foundation  border androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  BasicTextField "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  FocusRequester "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  	ImeAction "androidx.compose.foundation.layout  KeyboardActions "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OTPAutoReadTheme "androidx.compose.foundation.layout  
OTPDashBox "androidx.compose.foundation.layout  
OTPInputField "androidx.compose.foundation.layout  OTPViewModel "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  SimpleDashOTPDisplay "androidx.compose.foundation.layout  SimpleOTPScreen "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  	TextStyle "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  all "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  focusRequester "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  isDigit "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  joinToString "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  map "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  onFocusChanged "androidx.compose.foundation.layout  padEnd "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  repeat "androidx.compose.foundation.layout  replace "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  toList "androidx.compose.foundation.layout  
toMutableList "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  BasicTextField +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  	ImeAction +androidx.compose.foundation.layout.BoxScope  KeyboardActions +androidx.compose.foundation.layout.BoxScope  KeyboardOptions +androidx.compose.foundation.layout.BoxScope  KeyboardType +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  	TextStyle +androidx.compose.foundation.layout.BoxScope  all +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  focusRequester +androidx.compose.foundation.layout.BoxScope  isDigit +androidx.compose.foundation.layout.BoxScope  isEmpty +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  onFocusChanged +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  LaunchedEffect .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  
OTPInputField .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  SimpleDashOTPDisplay .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  Unit .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  joinToString .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  repeat .androidx.compose.foundation.layout.ColumnScope  replace .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  
toMutableList .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  
OTPDashBox +androidx.compose.foundation.layout.RowScope  
OTPInputField +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  joinToString +androidx.compose.foundation.layout.RowScope  repeat +androidx.compose.foundation.layout.RowScope  replace +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  
toMutableList +androidx.compose.foundation.layout.RowScope  RoundedCornerShape !androidx.compose.foundation.shape  BasicTextField  androidx.compose.foundation.text  KeyboardActionScope  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  BasicTextField androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  FocusRequester androidx.compose.material3  
FontWeight androidx.compose.material3  	ImeAction androidx.compose.material3  KeyboardActions androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OTPAutoReadTheme androidx.compose.material3  
OTPDashBox androidx.compose.material3  
OTPInputField androidx.compose.material3  OTPViewModel androidx.compose.material3  Preview androidx.compose.material3  Row androidx.compose.material3  SimpleDashOTPDisplay androidx.compose.material3  SimpleOTPScreen androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  	TextStyle androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  all androidx.compose.material3  
cardColors androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  focusRequester androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  isDigit androidx.compose.material3  isEmpty androidx.compose.material3  
isNotEmpty androidx.compose.material3  joinToString androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  map androidx.compose.material3  mutableStateOf androidx.compose.material3  onFocusChanged androidx.compose.material3  padEnd androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  repeat androidx.compose.material3  replace androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  take androidx.compose.material3  toList androidx.compose.material3  
toMutableList androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  outline &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  labelMedium %androidx.compose.material3.Typography  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  BasicTextField androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  FocusRequester androidx.compose.runtime  
FontWeight androidx.compose.runtime  	ImeAction androidx.compose.runtime  KeyboardActions androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OTPAutoReadTheme androidx.compose.runtime  
OTPDashBox androidx.compose.runtime  
OTPInputField androidx.compose.runtime  OTPViewModel androidx.compose.runtime  Preview androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Row androidx.compose.runtime  SimpleDashOTPDisplay androidx.compose.runtime  SimpleOTPScreen androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  Surface androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  	TextStyle androidx.compose.runtime  Unit androidx.compose.runtime  all androidx.compose.runtime  
cardColors androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  focusRequester androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  isDigit androidx.compose.runtime  isEmpty androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  joinToString androidx.compose.runtime  let androidx.compose.runtime  map androidx.compose.runtime  mutableStateOf androidx.compose.runtime  onFocusChanged androidx.compose.runtime  padEnd androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  repeat androidx.compose.runtime  replace androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  take androidx.compose.runtime  toList androidx.compose.runtime  
toMutableList androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction0  observeAsState !androidx.compose.runtime.livedata  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  focusRequester androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  onFocusChanged androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  FocusManager androidx.compose.ui.focus  FocusRequester androidx.compose.ui.focus  
FocusState androidx.compose.ui.focus  focusRequester androidx.compose.ui.focus  onFocusChanged androidx.compose.ui.focus  
clearFocus &androidx.compose.ui.focus.FocusManager  requestFocus (androidx.compose.ui.focus.FocusRequester  	isFocused $androidx.compose.ui.focus.FocusState  Color androidx.compose.ui.graphics  copy "androidx.compose.ui.graphics.Color  LocalContext androidx.compose.ui.platform  LocalFocusManager androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  	Companion (androidx.compose.ui.text.input.ImeAction  Done (androidx.compose.ui.text.input.ImeAction  Next (androidx.compose.ui.text.input.ImeAction  Done 2androidx.compose.ui.text.input.ImeAction.Companion  Next 2androidx.compose.ui.text.input.ImeAction.Companion  	Companion +androidx.compose.ui.text.input.KeyboardType  Number +androidx.compose.ui.text.input.KeyboardType  Number 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  OTPAutoReadTheme #androidx.core.app.ComponentActivity  SimpleOTPScreen #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  AndroidViewModel androidx.lifecycle  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  viewModelScope androidx.lifecycle  	onCleared #androidx.lifecycle.AndroidViewModel  observeAsState androidx.lifecycle.LiveData  value androidx.lifecycle.LiveData  value "androidx.lifecycle.MutableLiveData  	onCleared androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  	Alignment com.example.otpautoread  Arrangement com.example.otpautoread  Boolean com.example.otpautoread  Box com.example.otpautoread  Bundle com.example.otpautoread  Card com.example.otpautoread  CardDefaults com.example.otpautoread  CircularProgressIndicator com.example.otpautoread  Column com.example.otpautoread  ComponentActivity com.example.otpautoread  
Composable com.example.otpautoread  
FontWeight com.example.otpautoread  LaunchedEffect com.example.otpautoread  MainActivity com.example.otpautoread  
MaterialTheme com.example.otpautoread  Modifier com.example.otpautoread  OTPAutoReadTheme com.example.otpautoread  
OTPDashBox com.example.otpautoread  OTPViewModel com.example.otpautoread  Preview com.example.otpautoread  Row com.example.otpautoread  SimpleDashOTPDisplay com.example.otpautoread  SimpleOTPScreen com.example.otpautoread  SimpleOTPScreenPreview com.example.otpautoread  Spacer com.example.otpautoread  String com.example.otpautoread  Surface com.example.otpautoread  Text com.example.otpautoread  	TextAlign com.example.otpautoread  Unit com.example.otpautoread  
cardColors com.example.otpautoread  fillMaxSize com.example.otpautoread  fillMaxWidth com.example.otpautoread  getValue com.example.otpautoread  height com.example.otpautoread  
isNotEmpty com.example.otpautoread  padEnd com.example.otpautoread  padding com.example.otpautoread  provideDelegate com.example.otpautoread  repeat com.example.otpautoread  size com.example.otpautoread  spacedBy com.example.otpautoread  take com.example.otpautoread  toList com.example.otpautoread  OTPAutoReadTheme $com.example.otpautoread.MainActivity  SimpleOTPScreen $com.example.otpautoread.MainActivity  enableEdgeToEdge $com.example.otpautoread.MainActivity  
setContent $com.example.otpautoread.MainActivity  Arrays com.example.otpautoread.sms  Base64 com.example.otpautoread.sms  Boolean com.example.otpautoread.sms  BroadcastReceiver com.example.otpautoread.sms  Build com.example.otpautoread.sms  Bundle com.example.otpautoread.sms  CommonStatusCodes com.example.otpautoread.sms  Context com.example.otpautoread.sms  	Exception com.example.otpautoread.sms  	HASH_TYPE com.example.otpautoread.sms  Intent com.example.otpautoread.sms  IntentFilter com.example.otpautoread.sms  Log com.example.otpautoread.sms  
MessageDigest com.example.otpautoread.sms  NUM_BASE64_CHAR com.example.otpautoread.sms  NUM_HASHED_BYTES com.example.otpautoread.sms  NoSuchAlgorithmException com.example.otpautoread.sms  Regex com.example.otpautoread.sms  RegexOption com.example.otpautoread.sms  RequiresApi com.example.otpautoread.sms  SMSBroadcastReceiver com.example.otpautoread.sms  SMSRetrieverHelper com.example.otpautoread.sms  SmsRetriever com.example.otpautoread.sms  Status com.example.otpautoread.sms  String com.example.otpautoread.sms  TAG com.example.otpautoread.sms  Unit com.example.otpautoread.sms  also com.example.otpautoread.sms  android com.example.otpautoread.sms  apply com.example.otpautoread.sms  firstOrNull com.example.otpautoread.sms  
isNotEmpty com.example.otpautoread.sms  
isNullOrEmpty com.example.otpautoread.sms  let com.example.otpautoread.sms  listOf com.example.otpautoread.sms  matches com.example.otpautoread.sms  run com.example.otpautoread.sms  	substring com.example.otpautoread.sms  toByteArray com.example.otpautoread.sms  Boolean 0com.example.otpautoread.sms.SMSBroadcastReceiver  Bundle 0com.example.otpautoread.sms.SMSBroadcastReceiver  CommonStatusCodes 0com.example.otpautoread.sms.SMSBroadcastReceiver  Context 0com.example.otpautoread.sms.SMSBroadcastReceiver  	Exception 0com.example.otpautoread.sms.SMSBroadcastReceiver  Intent 0com.example.otpautoread.sms.SMSBroadcastReceiver  Log 0com.example.otpautoread.sms.SMSBroadcastReceiver  Regex 0com.example.otpautoread.sms.SMSBroadcastReceiver  RegexOption 0com.example.otpautoread.sms.SMSBroadcastReceiver  SmsRetriever 0com.example.otpautoread.sms.SMSBroadcastReceiver  Status 0com.example.otpautoread.sms.SMSBroadcastReceiver  String 0com.example.otpautoread.sms.SMSBroadcastReceiver  TAG 0com.example.otpautoread.sms.SMSBroadcastReceiver  Unit 0com.example.otpautoread.sms.SMSBroadcastReceiver  apply 0com.example.otpautoread.sms.SMSBroadcastReceiver  extractOTPFromMessage 0com.example.otpautoread.sms.SMSBroadcastReceiver  handleSuccessfulSMSRetrieval 0com.example.otpautoread.sms.SMSBroadcastReceiver  
isNotEmpty 0com.example.otpautoread.sms.SMSBroadcastReceiver  
isNullOrEmpty 0com.example.otpautoread.sms.SMSBroadcastReceiver  let 0com.example.otpautoread.sms.SMSBroadcastReceiver  listOf 0com.example.otpautoread.sms.SMSBroadcastReceiver  matches 0com.example.otpautoread.sms.SMSBroadcastReceiver  onErrorListener 0com.example.otpautoread.sms.SMSBroadcastReceiver  onSmsReceivedListener 0com.example.otpautoread.sms.SMSBroadcastReceiver  setOnErrorListener 0com.example.otpautoread.sms.SMSBroadcastReceiver  setOnSmsReceivedListener 0com.example.otpautoread.sms.SMSBroadcastReceiver  CommonStatusCodes :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  Log :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  Regex :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  RegexOption :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  SmsRetriever :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  TAG :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  
isNotEmpty :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  
isNullOrEmpty :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  listOf :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  matches :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  Arrays .com.example.otpautoread.sms.SMSRetrieverHelper  Base64 .com.example.otpautoread.sms.SMSRetrieverHelper  Build .com.example.otpautoread.sms.SMSRetrieverHelper  Context .com.example.otpautoread.sms.SMSRetrieverHelper  	Exception .com.example.otpautoread.sms.SMSRetrieverHelper  	HASH_TYPE .com.example.otpautoread.sms.SMSRetrieverHelper  IntentFilter .com.example.otpautoread.sms.SMSRetrieverHelper  Log .com.example.otpautoread.sms.SMSRetrieverHelper  
MessageDigest .com.example.otpautoread.sms.SMSRetrieverHelper  NUM_BASE64_CHAR .com.example.otpautoread.sms.SMSRetrieverHelper  NUM_HASHED_BYTES .com.example.otpautoread.sms.SMSRetrieverHelper  NoSuchAlgorithmException .com.example.otpautoread.sms.SMSRetrieverHelper  Regex .com.example.otpautoread.sms.SMSRetrieverHelper  RequiresApi .com.example.otpautoread.sms.SMSRetrieverHelper  SMSBroadcastReceiver .com.example.otpautoread.sms.SMSRetrieverHelper  SmsRetriever .com.example.otpautoread.sms.SMSRetrieverHelper  String .com.example.otpautoread.sms.SMSRetrieverHelper  TAG .com.example.otpautoread.sms.SMSRetrieverHelper  Unit .com.example.otpautoread.sms.SMSRetrieverHelper  also .com.example.otpautoread.sms.SMSRetrieverHelper  android .com.example.otpautoread.sms.SMSRetrieverHelper  apply .com.example.otpautoread.sms.SMSRetrieverHelper  context .com.example.otpautoread.sms.SMSRetrieverHelper  firstOrNull .com.example.otpautoread.sms.SMSRetrieverHelper  generateHash .com.example.otpautoread.sms.SMSRetrieverHelper  
getAppHash .com.example.otpautoread.sms.SMSRetrieverHelper  let .com.example.otpautoread.sms.SMSRetrieverHelper  registerSMSReceiver .com.example.otpautoread.sms.SMSRetrieverHelper  run .com.example.otpautoread.sms.SMSRetrieverHelper  smsReceiver .com.example.otpautoread.sms.SMSRetrieverHelper  startSMSRetriever .com.example.otpautoread.sms.SMSRetrieverHelper  	substring .com.example.otpautoread.sms.SMSRetrieverHelper  toByteArray .com.example.otpautoread.sms.SMSRetrieverHelper  unregisterSMSReceiver .com.example.otpautoread.sms.SMSRetrieverHelper  Arrays 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  Base64 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  Build 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  	HASH_TYPE 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  IntentFilter 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  Log 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  
MessageDigest 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  NUM_BASE64_CHAR 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  NUM_HASHED_BYTES 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  Regex 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  SMSBroadcastReceiver 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  SmsRetriever 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  TAG 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  also 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  android 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  apply 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  firstOrNull 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  let 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  run 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  	substring 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  toByteArray 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  	Alignment %com.example.otpautoread.ui.components  Arrangement %com.example.otpautoread.ui.components  BasicTextField %com.example.otpautoread.ui.components  Boolean %com.example.otpautoread.ui.components  Box %com.example.otpautoread.ui.components  Card %com.example.otpautoread.ui.components  CardDefaults %com.example.otpautoread.ui.components  CircularProgressIndicator %com.example.otpautoread.ui.components  Column %com.example.otpautoread.ui.components  
Composable %com.example.otpautoread.ui.components  FocusRequester %com.example.otpautoread.ui.components  
FontWeight %com.example.otpautoread.ui.components  	ImeAction %com.example.otpautoread.ui.components  KeyboardActions %com.example.otpautoread.ui.components  KeyboardOptions %com.example.otpautoread.ui.components  KeyboardType %com.example.otpautoread.ui.components  LaunchedEffect %com.example.otpautoread.ui.components  List %com.example.otpautoread.ui.components  
MaterialTheme %com.example.otpautoread.ui.components  Modifier %com.example.otpautoread.ui.components  OTPInputComponent %com.example.otpautoread.ui.components  OTPInputComponentErrorPreview %com.example.otpautoread.ui.components  OTPInputComponentLoadingPreview %com.example.otpautoread.ui.components  OTPInputComponentPreview %com.example.otpautoread.ui.components  
OTPInputField %com.example.otpautoread.ui.components  Preview %com.example.otpautoread.ui.components  Row %com.example.otpautoread.ui.components  Spacer %com.example.otpautoread.ui.components  String %com.example.otpautoread.ui.components  Text %com.example.otpautoread.ui.components  	TextAlign %com.example.otpautoread.ui.components  	TextStyle %com.example.otpautoread.ui.components  Unit %com.example.otpautoread.ui.components  all %com.example.otpautoread.ui.components  
cardColors %com.example.otpautoread.ui.components  fillMaxSize %com.example.otpautoread.ui.components  fillMaxWidth %com.example.otpautoread.ui.components  focusRequester %com.example.otpautoread.ui.components  getValue %com.example.otpautoread.ui.components  height %com.example.otpautoread.ui.components  isDigit %com.example.otpautoread.ui.components  isEmpty %com.example.otpautoread.ui.components  
isNotEmpty %com.example.otpautoread.ui.components  joinToString %com.example.otpautoread.ui.components  let %com.example.otpautoread.ui.components  map %com.example.otpautoread.ui.components  mutableStateOf %com.example.otpautoread.ui.components  onFocusChanged %com.example.otpautoread.ui.components  padEnd %com.example.otpautoread.ui.components  padding %com.example.otpautoread.ui.components  provideDelegate %com.example.otpautoread.ui.components  remember %com.example.otpautoread.ui.components  repeat %com.example.otpautoread.ui.components  replace %com.example.otpautoread.ui.components  setValue %com.example.otpautoread.ui.components  size %com.example.otpautoread.ui.components  spacedBy %com.example.otpautoread.ui.components  take %com.example.otpautoread.ui.components  toList %com.example.otpautoread.ui.components  
toMutableList %com.example.otpautoread.ui.components  Boolean  com.example.otpautoread.ui.theme  Build  com.example.otpautoread.ui.theme  
Composable  com.example.otpautoread.ui.theme  DarkColorScheme  com.example.otpautoread.ui.theme  
FontFamily  com.example.otpautoread.ui.theme  
FontWeight  com.example.otpautoread.ui.theme  LightColorScheme  com.example.otpautoread.ui.theme  OTPAutoReadTheme  com.example.otpautoread.ui.theme  Pink40  com.example.otpautoread.ui.theme  Pink80  com.example.otpautoread.ui.theme  Purple40  com.example.otpautoread.ui.theme  Purple80  com.example.otpautoread.ui.theme  PurpleGrey40  com.example.otpautoread.ui.theme  PurpleGrey80  com.example.otpautoread.ui.theme  
Typography  com.example.otpautoread.ui.theme  Unit  com.example.otpautoread.ui.theme  AndroidViewModel !com.example.otpautoread.viewmodel  Application !com.example.otpautoread.viewmodel  Boolean !com.example.otpautoread.viewmodel  	Exception !com.example.otpautoread.viewmodel  LiveData !com.example.otpautoread.viewmodel  Log !com.example.otpautoread.viewmodel  MutableLiveData !com.example.otpautoread.viewmodel  OTPViewModel !com.example.otpautoread.viewmodel  Regex !com.example.otpautoread.viewmodel  SMSRetrieverHelper !com.example.otpautoread.viewmodel  SMS_TIMEOUT_DURATION !com.example.otpautoread.viewmodel  String !com.example.otpautoread.viewmodel  TAG !com.example.otpautoread.viewmodel  _appHash !com.example.otpautoread.viewmodel  
_errorMessage !com.example.otpautoread.viewmodel  
_isLoading !com.example.otpautoread.viewmodel  _isOtpVerified !com.example.otpautoread.viewmodel  	_otpValue !com.example.otpautoread.viewmodel  _smsRetrievalActive !com.example.otpautoread.viewmodel  all !com.example.otpautoread.viewmodel  
clearError !com.example.otpautoread.viewmodel  delay !com.example.otpautoread.viewmodel  handleSMSError !com.example.otpautoread.viewmodel  isDigit !com.example.otpautoread.viewmodel  
isNotEmpty !com.example.otpautoread.viewmodel  
isValidOTP !com.example.otpautoread.viewmodel  launch !com.example.otpautoread.viewmodel  matches !com.example.otpautoread.viewmodel  smsRetrieverHelper !com.example.otpautoread.viewmodel  	verifyOTP !com.example.otpautoread.viewmodel  Application .com.example.otpautoread.viewmodel.OTPViewModel  Boolean .com.example.otpautoread.viewmodel.OTPViewModel  	Exception .com.example.otpautoread.viewmodel.OTPViewModel  LiveData .com.example.otpautoread.viewmodel.OTPViewModel  Log .com.example.otpautoread.viewmodel.OTPViewModel  MutableLiveData .com.example.otpautoread.viewmodel.OTPViewModel  Regex .com.example.otpautoread.viewmodel.OTPViewModel  SMSRetrieverHelper .com.example.otpautoread.viewmodel.OTPViewModel  SMS_TIMEOUT_DURATION .com.example.otpautoread.viewmodel.OTPViewModel  String .com.example.otpautoread.viewmodel.OTPViewModel  TAG .com.example.otpautoread.viewmodel.OTPViewModel  _appHash .com.example.otpautoread.viewmodel.OTPViewModel  
_errorMessage .com.example.otpautoread.viewmodel.OTPViewModel  
_isLoading .com.example.otpautoread.viewmodel.OTPViewModel  _isOtpVerified .com.example.otpautoread.viewmodel.OTPViewModel  	_otpValue .com.example.otpautoread.viewmodel.OTPViewModel  _smsRetrievalActive .com.example.otpautoread.viewmodel.OTPViewModel  all .com.example.otpautoread.viewmodel.OTPViewModel  appHash .com.example.otpautoread.viewmodel.OTPViewModel  
clearError .com.example.otpautoread.viewmodel.OTPViewModel  delay .com.example.otpautoread.viewmodel.OTPViewModel  errorMessage .com.example.otpautoread.viewmodel.OTPViewModel  generateAppHash .com.example.otpautoread.viewmodel.OTPViewModel  handleSMSError .com.example.otpautoread.viewmodel.OTPViewModel  handleSMSReceived .com.example.otpautoread.viewmodel.OTPViewModel  invoke .com.example.otpautoread.viewmodel.OTPViewModel  isDigit .com.example.otpautoread.viewmodel.OTPViewModel  	isLoading .com.example.otpautoread.viewmodel.OTPViewModel  
isNotEmpty .com.example.otpautoread.viewmodel.OTPViewModel  
isOtpVerified .com.example.otpautoread.viewmodel.OTPViewModel  
isValidOTP .com.example.otpautoread.viewmodel.OTPViewModel  launch .com.example.otpautoread.viewmodel.OTPViewModel  matches .com.example.otpautoread.viewmodel.OTPViewModel  otpValue .com.example.otpautoread.viewmodel.OTPViewModel  setupSMSTimeout .com.example.otpautoread.viewmodel.OTPViewModel  smsRetrieverHelper .com.example.otpautoread.viewmodel.OTPViewModel  startSMSRetriever .com.example.otpautoread.viewmodel.OTPViewModel  stopSMSRetriever .com.example.otpautoread.viewmodel.OTPViewModel  	verifyOTP .com.example.otpautoread.viewmodel.OTPViewModel  viewModelScope .com.example.otpautoread.viewmodel.OTPViewModel  Log 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  MutableLiveData 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  Regex 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  SMSRetrieverHelper 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  SMS_TIMEOUT_DURATION 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  TAG 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  _appHash 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  
_errorMessage 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  
_isLoading 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  _isOtpVerified 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  	_otpValue 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  _smsRetrievalActive 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  all 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  
clearError 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  delay 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  handleSMSError 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  isDigit 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  
isNotEmpty 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  
isValidOTP 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  launch 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  matches 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  smsRetrieverHelper 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  	verifyOTP 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  viewModelScope 8com.example.otpautoread.viewmodel.OTPViewModel.Companion  SmsRetriever %com.google.android.gms.auth.api.phone  SmsRetrieverClient %com.google.android.gms.auth.api.phone  EXTRA_SMS_MESSAGE 2com.google.android.gms.auth.api.phone.SmsRetriever  EXTRA_STATUS 2com.google.android.gms.auth.api.phone.SmsRetriever  SMS_RETRIEVED_ACTION 2com.google.android.gms.auth.api.phone.SmsRetriever  	getClient 2com.google.android.gms.auth.api.phone.SmsRetriever  startSmsRetriever 8com.google.android.gms.auth.api.phone.SmsRetrieverClient  CommonStatusCodes !com.google.android.gms.common.api  Status !com.google.android.gms.common.api  SUCCESS 3com.google.android.gms.common.api.CommonStatusCodes  TIMEOUT 3com.google.android.gms.common.api.CommonStatusCodes  
statusCode (com.google.android.gms.common.api.Status  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  	Exception 	java.lang  message java.lang.Exception  
MessageDigest 
java.security  NoSuchAlgorithmException 
java.security  digest java.security.MessageDigest  getInstance java.security.MessageDigest  update java.security.MessageDigest  Arrays 	java.util  Base64 	java.util  Build 	java.util  Context 	java.util  	Exception 	java.util  	HASH_TYPE 	java.util  IntentFilter 	java.util  Log 	java.util  
MessageDigest 	java.util  NUM_BASE64_CHAR 	java.util  NUM_HASHED_BYTES 	java.util  NoSuchAlgorithmException 	java.util  Regex 	java.util  RequiresApi 	java.util  SMSBroadcastReceiver 	java.util  SmsRetriever 	java.util  String 	java.util  TAG 	java.util  Unit 	java.util  also 	java.util  android 	java.util  apply 	java.util  firstOrNull 	java.util  let 	java.util  run 	java.util  	substring 	java.util  toByteArray 	java.util  copyOfRange java.util.Arrays  
getEncoder java.util.Base64  encodeToString java.util.Base64.Encoder  	ByteArray kotlin  CharSequence kotlin  	Exception kotlin  	Function0 kotlin  	Function1 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  also kotlin  apply kotlin  let kotlin  map kotlin  repeat kotlin  run kotlin  toList kotlin  not kotlin.Boolean  isDigit kotlin.Char  toString kotlin.Char  isEmpty kotlin.CharSequence  sp 
kotlin.Double  message kotlin.Exception  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  all 
kotlin.String  also 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  matches 
kotlin.String  padEnd 
kotlin.String  replace 
kotlin.String  take 
kotlin.String  toByteArray 
kotlin.String  toList 
kotlin.String  message kotlin.Throwable  Iterator kotlin.collections  List kotlin.collections  MutableList kotlin.collections  all kotlin.collections  firstOrNull kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  take kotlin.collections  toByteArray kotlin.collections  toList kotlin.collections  
toMutableList kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  get kotlin.collections.List  iterator kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  
toMutableList kotlin.collections.List  joinToString kotlin.collections.MutableList  set kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  firstOrNull 
kotlin.ranges  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  Sequence kotlin.sequences  all kotlin.sequences  firstOrNull kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  
toMutableList kotlin.sequences  MatchResult kotlin.text  Regex kotlin.text  RegexOption kotlin.text  all kotlin.text  firstOrNull kotlin.text  isDigit kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  map kotlin.text  matches kotlin.text  padEnd kotlin.text  repeat kotlin.text  replace kotlin.text  	substring kotlin.text  take kotlin.text  toByteArray kotlin.text  toList kotlin.text  
toMutableList kotlin.text  groupValues kotlin.text.MatchResult  value kotlin.text.MatchResult  find kotlin.text.Regex  pattern kotlin.text.Regex  IGNORE_CASE kotlin.text.RegexOption  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  Log !kotlinx.coroutines.CoroutineScope  SMS_TIMEOUT_DURATION !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  _appHash !kotlinx.coroutines.CoroutineScope  
_errorMessage !kotlinx.coroutines.CoroutineScope  
_isLoading !kotlinx.coroutines.CoroutineScope  _isOtpVerified !kotlinx.coroutines.CoroutineScope  	_otpValue !kotlinx.coroutines.CoroutineScope  _smsRetrievalActive !kotlinx.coroutines.CoroutineScope  
clearError !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  handleSMSError !kotlinx.coroutines.CoroutineScope  
isValidOTP !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  smsRetrieverHelper !kotlinx.coroutines.CoroutineScope  	verifyOTP !kotlinx.coroutines.CoroutineScope  
ContextCompat androidx.core.content  RECEIVER_NOT_EXPORTED #androidx.core.content.ContextCompat  registerReceiver #androidx.core.content.ContextCompat  
ContextCompat com.example.otpautoread.sms  
ContextCompat .com.example.otpautoread.sms.SMSRetrieverHelper  
ContextCompat 8com.example.otpautoread.sms.SMSRetrieverHelper.Companion  
ContextCompat 	java.util  handleError .com.example.otpautoread.viewmodel.OTPViewModel  handleOTPReceived .com.example.otpautoread.viewmodel.OTPViewModel  replace !android.content.BroadcastReceiver  toRegex !android.content.BroadcastReceiver  trim !android.content.BroadcastReceiver  	withIndex !android.content.BroadcastReceiver  replace com.example.otpautoread.sms  toRegex com.example.otpautoread.sms  trim com.example.otpautoread.sms  	withIndex com.example.otpautoread.sms  replace 0com.example.otpautoread.sms.SMSBroadcastReceiver  toRegex 0com.example.otpautoread.sms.SMSBroadcastReceiver  trim 0com.example.otpautoread.sms.SMSBroadcastReceiver  	withIndex 0com.example.otpautoread.sms.SMSBroadcastReceiver  replace :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  toRegex :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  trim :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  	withIndex :com.example.otpautoread.sms.SMSBroadcastReceiver.Companion  toRegex 
kotlin.String  trim 
kotlin.String  IndexedValue kotlin.collections  Iterable kotlin.collections  	withIndex kotlin.collections  
component1 kotlin.collections.IndexedValue  
component2 kotlin.collections.IndexedValue  iterator kotlin.collections.Iterable  	withIndex kotlin.collections.List  	withIndex kotlin.sequences  toRegex kotlin.text  trim kotlin.text  	withIndex kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             