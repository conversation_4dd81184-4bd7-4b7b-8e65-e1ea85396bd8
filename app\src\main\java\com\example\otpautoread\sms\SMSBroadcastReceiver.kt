package com.example.otpautoread.sms

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.common.api.Status

/**
 * BroadcastReceiver for handling SMS messages through SMS Retriever API
 * 
 * This receiver:
 * 1. Listens for SMS_RETRIEVED_ACTION broadcasts from Google Play Services
 * 2. Extracts SMS content from the intent
 * 3. Parses OTP from the SMS message
 * 4. Notifies the app through callback functions
 * 
 * The SMS Retriever API only works with SMS messages that:
 * - Contain a 4-11 character alphanumeric string with at least one number
 * - Include your app's hash string
 * - Are sent to the device within 5 minutes of calling startSmsRetriever()
 */
class SMSBroadcastReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "SMSBroadcastReceiver"
    }

    // Callback functions for handling SMS reception and errors
    private var onSmsReceivedListener: ((String) -> Unit)? = null
    private var onErrorListener: ((String) -> Unit)? = null

    /**
     * Sets the callback function to be called when OTP is successfully extracted
     * 
     * @param listener Function that receives the extracted OTP string
     */
    fun setOnSmsReceivedListener(listener: (String) -> Unit) {
        this.onSmsReceivedListener = listener
    }

    /**
     * Sets the callback function to be called when an error occurs
     * 
     * @param listener Function that receives error message string
     */
    fun setOnErrorListener(listener: (String) -> Unit) {
        this.onErrorListener = listener
    }

    /**
     * Called when a broadcast is received
     * 
     * This method handles the SMS_RETRIEVED_ACTION broadcast sent by Google Play Services
     * when an SMS message matching the criteria is received.
     * 
     * @param context The context in which the receiver is running
     * @param intent The intent containing the SMS data
     */
    override fun onReceive(context: Context?, intent: Intent?) {
        Log.d(TAG, "Broadcast received with action: ${intent?.action}")

        // Verify this is the SMS retriever action
        if (intent?.action != SmsRetriever.SMS_RETRIEVED_ACTION) {
            Log.w(TAG, "Received unexpected action: ${intent?.action}")
            return
        }

        try {
            // Extract the SMS retriever result from the intent
            val extras: Bundle? = intent.extras
            val smsRetrieverStatus = extras?.get(SmsRetriever.EXTRA_STATUS) as? Status

            when (smsRetrieverStatus?.statusCode) {
                CommonStatusCodes.SUCCESS -> {
                    // SMS successfully retrieved
                    handleSuccessfulSMSRetrieval(extras)
                }
                
                CommonStatusCodes.TIMEOUT -> {
                    // SMS retriever timed out (5 minutes)
                    Log.w(TAG, "SMS Retriever timed out")
                    onErrorListener?.invoke("SMS verification timed out. Please try again.")
                }
                
                else -> {
                    // Other error occurred
                    val errorMessage = "SMS Retriever failed with status: ${smsRetrieverStatus?.statusCode}"
                    Log.e(TAG, errorMessage)
                    onErrorListener?.invoke(errorMessage)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error processing SMS retriever broadcast", e)
            onErrorListener?.invoke("Error processing SMS: ${e.message}")
        }
    }

    /**
     * Handles successful SMS retrieval and extracts OTP
     * 
     * @param extras The bundle containing SMS data
     */
    private fun handleSuccessfulSMSRetrieval(extras: Bundle) {
        try {
            // Get the SMS message from the intent
            val smsMessage = extras.getString(SmsRetriever.EXTRA_SMS_MESSAGE)
            
            if (smsMessage.isNullOrEmpty()) {
                Log.e(TAG, "SMS message is null or empty")
                onErrorListener?.invoke("Received empty SMS message")
                return
            }

            Log.d(TAG, "SMS message received: $smsMessage")

            // Extract OTP from the SMS message
            val extractedOTP = extractOTPFromMessage(smsMessage)
            
            if (extractedOTP != null) {
                Log.d(TAG, "OTP successfully extracted: $extractedOTP")
                onSmsReceivedListener?.invoke(extractedOTP)
            } else {
                Log.w(TAG, "Could not extract OTP from SMS message")
                onErrorListener?.invoke("Could not find OTP in the received message")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error handling successful SMS retrieval", e)
            onErrorListener?.invoke("Error extracting OTP: ${e.message}")
        }
    }

    /**
     * Extracts OTP code from SMS message content
     * 
     * This method uses multiple regex patterns to find OTP codes in various formats:
     * 1. 4-6 digit numbers (most common)
     * 2. Numbers preceded by common OTP keywords
     * 3. Numbers in specific formats like "code: 1234"
     * 
     * @param message The SMS message content
     * @return The extracted OTP string or null if not found
     */
    private fun extractOTPFromMessage(message: String): String? {
        try {
            // List of regex patterns to match different OTP formats
            val otpPatterns = listOf(
                // Standard 4-6 digit OTP
                Regex("\\b\\d{4,6}\\b"),
                
                // OTP with keywords (case insensitive)
                Regex("(?i)(?:otp|code|pin|verification|verify)\\s*:?\\s*(\\d{4,6})", RegexOption.IGNORE_CASE),
                
                // OTP in brackets or parentheses
                Regex("\\[(\\d{4,6})\\]"),
                Regex("\\((\\d{4,6})\\)"),
                
                // OTP with specific phrases
                Regex("(?i)your\\s+(?:otp|code|pin)\\s+is\\s*:?\\s*(\\d{4,6})", RegexOption.IGNORE_CASE),
                Regex("(?i)verification\\s+code\\s*:?\\s*(\\d{4,6})", RegexOption.IGNORE_CASE)
            )

            // Try each pattern until we find a match
            for (pattern in otpPatterns) {
                val matchResult = pattern.find(message)
                if (matchResult != null) {
                    // If the pattern has groups, use the first group, otherwise use the whole match
                    val otp = if (matchResult.groupValues.size > 1) {
                        matchResult.groupValues[1]
                    } else {
                        matchResult.value
                    }
                    
                    // Validate that the extracted OTP is 4-6 digits
                    if (otp.matches(Regex("\\d{4,6}"))) {
                        Log.d(TAG, "OTP extracted using pattern: ${pattern.pattern}")
                        return otp
                    }
                }
            }

            Log.w(TAG, "No OTP pattern matched in message: $message")
            return null

        } catch (e: Exception) {
            Log.e(TAG, "Error extracting OTP from message", e)
            return null
        }
    }

    /**
     * Validates if the extracted string is a valid OTP
     * 
     * @param otp The string to validate
     * @return true if valid OTP, false otherwise
     */
    private fun isValidOTP(otp: String): Boolean {
        return otp.matches(Regex("\\d{4,6}")) && otp.isNotEmpty()
    }

    /**
     * Cleans up the receiver by clearing callback listeners
     * 
     * This should be called when the receiver is no longer needed
     * to prevent memory leaks
     */
    fun cleanup() {
        onSmsReceivedListener = null
        onErrorListener = null
        Log.d(TAG, "SMS Broadcast Receiver cleaned up")
    }
}
