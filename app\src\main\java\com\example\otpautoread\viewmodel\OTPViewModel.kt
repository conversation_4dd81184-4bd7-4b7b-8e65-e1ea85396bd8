package com.example.otpautoread.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.otpautoread.sms.SMSRetrieverHelper
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * ViewModel for managing OTP verification state and SMS retrieval
 * 
 * This ViewModel handles:
 * 1. OTP input state management
 * 2. SMS Retriever API integration
 * 3. Loading states and error handling
 * 4. Auto-verification logic
 * 5. Timeout management
 * 
 * The ViewModel follows MVVM architecture pattern and provides
 * a clean separation between UI and business logic.
 */
class OTPViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        private const val TAG = "OTPViewModel"
        private const val SMS_TIMEOUT_DURATION = 300000L // 5 minutes in milliseconds
    }

    private val smsRetrieverHelper = SMSRetriever<PERSON>elper(application)

    // OTP state management
    private val _otpValue = MutableLiveData<String>("")
    val otpValue: LiveData<String> = _otpValue

    // Loading state for SMS retrieval
    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    // Error state management
    private val _errorMessage = MutableLiveData<String?>(null)
    val errorMessage: LiveData<String?> = _errorMessage

    // Success state for OTP verification
    private val _isOtpVerified = MutableLiveData<Boolean>(false)
    val isOtpVerified: LiveData<Boolean> = _isOtpVerified

    // SMS retrieval status
    private val _smsRetrievalActive = MutableLiveData<Boolean>(false)
    val smsRetrievalActive: LiveData<Boolean> = _smsRetrievalActive

    // App hash for SMS verification
    private val _appHash = MutableLiveData<String>("")
    val appHash: LiveData<String> = _appHash

    init {
        // Generate and store app hash on initialization
        generateAppHash()
    }

    /**
     * Starts the SMS retriever and begins listening for OTP SMS
     * 
     * This method:
     * 1. Clears any previous state
     * 2. Starts the SMS Retriever API
     * 3. Sets up timeout handling
     * 4. Updates loading state
     */
    fun startSMSRetriever() {
        Log.d(TAG, "Starting SMS Retriever")
        
        // Clear previous state
        clearError()
        _isLoading.value = true
        _smsRetrievalActive.value = true
        _isOtpVerified.value = false

        try {
            smsRetrieverHelper.startSMSRetriever(
                onSmsReceived = { otp ->
                    handleSMSReceived(otp)
                },
                onError = { error ->
                    handleSMSError(error)
                }
            )

            // Set up timeout for SMS retrieval
            setupSMSTimeout()

        } catch (e: Exception) {
            Log.e(TAG, "Error starting SMS Retriever", e)
            handleSMSError("Failed to start SMS verification: ${e.message}")
        }
    }

    /**
     * Handles successful SMS reception and OTP extraction
     * 
     * @param otp The extracted OTP from SMS
     */
    private fun handleSMSReceived(otp: String) {
        Log.d(TAG, "SMS received with OTP: $otp")
        
        viewModelScope.launch {
            try {
                // Validate OTP format
                if (isValidOTP(otp)) {
                    _otpValue.value = otp
                    _isLoading.value = false
                    _smsRetrievalActive.value = false
                    clearError()
                    
                    // Auto-verify if OTP is complete
                    if (otp.length == 4) {
                        verifyOTP(otp)
                    }
                } else {
                    handleSMSError("Invalid OTP format received")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error handling received SMS", e)
                handleSMSError("Error processing received OTP: ${e.message}")
            }
        }
    }

    /**
     * Handles SMS retrieval errors
     * 
     * @param error The error message
     */
    private fun handleSMSError(error: String) {
        Log.e(TAG, "SMS Error: $error")
        
        viewModelScope.launch {
            _isLoading.value = false
            _smsRetrievalActive.value = false
            _errorMessage.value = error
        }
    }

    /**
     * Sets up timeout for SMS retrieval (5 minutes)
     */
    private fun setupSMSTimeout() {
        viewModelScope.launch {
            delay(SMS_TIMEOUT_DURATION)
            
            // Check if SMS retrieval is still active
            if (_smsRetrievalActive.value == true) {
                Log.w(TAG, "SMS Retriever timed out")
                handleSMSError("SMS verification timed out. Please try again or enter OTP manually.")
            }
        }
    }

    /**
     * Updates OTP value manually (when user types)
     * 
     * @param otp The new OTP value
     */
    fun updateOTP(otp: String) {
        if (otp.length <= 4 && otp.all { it.isDigit() }) {
            _otpValue.value = otp
            clearError()
            
            // Auto-verify when 4 digits are entered
            if (otp.length == 4) {
                verifyOTP(otp)
            }
        }
    }

    /**
     * Verifies the entered OTP
     * 
     * In a real app, this would make an API call to verify the OTP
     * For this demo, we'll simulate verification
     * 
     * @param otp The OTP to verify
     */
    fun verifyOTP(otp: String) {
        Log.d(TAG, "Verifying OTP: $otp")
        
        if (!isValidOTP(otp)) {
            _errorMessage.value = "Please enter a valid 4-digit OTP"
            return
        }

        viewModelScope.launch {
            try {
                _isLoading.value = true
                clearError()

                // Simulate API call delay
                delay(1000)

                // In a real app, you would make an API call here
                // For demo purposes, we'll accept any 4-digit OTP
                _isOtpVerified.value = true
                _isLoading.value = false
                _smsRetrievalActive.value = false
                
                Log.d(TAG, "OTP verified successfully")

            } catch (e: Exception) {
                Log.e(TAG, "Error verifying OTP", e)
                _isLoading.value = false
                _errorMessage.value = "Verification failed. Please try again."
            }
        }
    }

    /**
     * Resends OTP by restarting SMS retriever
     */
    fun resendOTP() {
        Log.d(TAG, "Resending OTP")
        
        // Clear current state
        _otpValue.value = ""
        _isOtpVerified.value = false
        clearError()
        
        // Restart SMS retriever
        startSMSRetriever()
    }

    /**
     * Clears error message
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * Validates OTP format
     * 
     * @param otp The OTP to validate
     * @return true if valid, false otherwise
     */
    private fun isValidOTP(otp: String): Boolean {
        return otp.matches(Regex("\\d{4}")) && otp.isNotEmpty()
    }

    /**
     * Generates app hash for SMS verification
     */
    private fun generateAppHash() {
        viewModelScope.launch {
            try {
                val hash = smsRetrieverHelper.getAppHash()
                _appHash.value = hash
                Log.d(TAG, "App hash generated: $hash")
            } catch (e: Exception) {
                Log.e(TAG, "Error generating app hash", e)
            }
        }
    }

    /**
     * Stops SMS retriever and cleans up resources
     */
    fun stopSMSRetriever() {
        Log.d(TAG, "Stopping SMS Retriever")
        
        try {
            smsRetrieverHelper.unregisterSMSReceiver()
            _smsRetrievalActive.value = false
            _isLoading.value = false
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping SMS Retriever", e)
        }
    }

    /**
     * Resets all state to initial values
     */
    fun resetState() {
        Log.d(TAG, "Resetting OTP state")
        
        _otpValue.value = ""
        _isLoading.value = false
        _errorMessage.value = null
        _isOtpVerified.value = false
        _smsRetrievalActive.value = false
        
        stopSMSRetriever()
    }

    /**
     * Called when ViewModel is cleared
     * Cleans up SMS retriever resources
     */
    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "ViewModel cleared, cleaning up resources")
        stopSMSRetriever()
    }
}
