package com.example.otpautoread

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.otpautoread.ui.components.OTPInputComponent
import com.example.otpautoread.ui.theme.OTPAutoReadTheme
import com.example.otpautoread.viewmodel.OTPViewModel

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            OTPAutoReadTheme {
                OTPVerificationScreen()
            }
        }
    }
}

/**
 * Main OTP Verification Screen
 *
 * This composable provides the complete OTP verification experience including:
 * - Automatic SMS detection and OTP extraction
 * - Manual OTP input capability
 * - Loading states and error handling
 * - Success confirmation
 * - App hash display for testing
 */
@Composable
fun OTPVerificationScreen(
    viewModel: OTPViewModel = viewModel()
) {
    // Observe ViewModel state
    val otpValue by viewModel.otpValue.observeAsState("")
    val isLoading by viewModel.isLoading.observeAsState(false)
    val errorMessage by viewModel.errorMessage.observeAsState()
    val isOtpVerified by viewModel.isOtpVerified.observeAsState(false)
    val smsRetrievalActive by viewModel.smsRetrievalActive.observeAsState(false)
    val appHash by viewModel.appHash.observeAsState("")

    // Start SMS retriever when screen is first displayed
    LaunchedEffect(Unit) {
        viewModel.startSMSRetriever()
    }

    // Create gradient background
    val gradientBrush = Brush.verticalGradient(
        colors = listOf(
            MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
            MaterialTheme.colorScheme.surface
        )
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = gradientBrush)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {

            // App Title
            Text(
                text = "OTP Auto Read",
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Text(
                text = "Automatic SMS Verification Demo",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 48.dp)
            )

            // Show success state or OTP input
            if (isOtpVerified) {
                SuccessCard(
                    otp = otpValue,
                    onTryAgain = {
                        viewModel.resetState()
                        viewModel.startSMSRetriever()
                    }
                )
            } else {
                // OTP Input Component
                OTPInputComponent(
                    otp = otpValue,
                    onOtpChange = { newOtp ->
                        viewModel.updateOTP(newOtp)
                    },
                    onOtpComplete = { completedOtp ->
                        viewModel.verifyOTP(completedOtp)
                    },
                    isLoading = isLoading,
                    error = errorMessage
                )

                Spacer(modifier = Modifier.height(32.dp))

                // Action Buttons
                ActionButtons(
                    isLoading = isLoading,
                    smsRetrievalActive = smsRetrievalActive,
                    onResendOtp = { viewModel.resendOTP() },
                    onManualEntry = { viewModel.stopSMSRetriever() }
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // App Hash Information (for testing)
            if (appHash.isNotEmpty()) {
                AppHashCard(appHash = appHash)
            }
        }
    }
}

/**
 * Success card displayed when OTP is verified
 */
@Composable
fun SuccessCard(
    otp: String,
    onTryAgain: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "✅",
                style = MaterialTheme.typography.displayMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = "Verification Successful!",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Text(
                text = "OTP: $otp",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f),
                modifier = Modifier.padding(bottom = 24.dp)
            )

            Button(
                onClick = onTryAgain,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Try Again")
            }
        }
    }
}

/**
 * Action buttons for resend and manual entry
 */
@Composable
fun ActionButtons(
    isLoading: Boolean,
    smsRetrievalActive: Boolean,
    onResendOtp: () -> Unit,
    onManualEntry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Resend OTP Button
        OutlinedButton(
            onClick = onResendOtp,
            enabled = !isLoading,
            modifier = Modifier.weight(1f)
        ) {
            Text("Resend OTP")
        }

        // Manual Entry Button
        if (smsRetrievalActive) {
            Button(
                onClick = onManualEntry,
                enabled = !isLoading,
                modifier = Modifier.weight(1f)
            ) {
                Text("Enter Manually")
            }
        }
    }
}

/**
 * Card displaying app hash for testing purposes
 */
@Composable
fun AppHashCard(
    appHash: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "App Hash (for testing)",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 4.dp)
            )

            Text(
                text = appHash,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f),
                modifier = Modifier.padding(bottom = 8.dp)
            )

            Text(
                text = "Include this hash in your test SMS: 'Your OTP is 1234 $appHash'",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun OTPVerificationScreenPreview() {
    OTPAutoReadTheme {
        // Note: Preview won't show ViewModel functionality
        Surface {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "OTP Auto Read",
                    style = MaterialTheme.typography.headlineLarge,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}