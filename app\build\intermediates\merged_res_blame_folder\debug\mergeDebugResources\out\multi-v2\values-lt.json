{"logs": [{"outputFile": "com.example.otpautoread.app-mergeDebugResources-54:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7d7640ada57095f48ab28f83984208c\\transformed\\core-1.16.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "2,3,4,5,6,7,8,98", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,313,412,515,626,736,10630", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "198,308,407,510,621,731,851,10726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0dcbd671948b5a784f1cdbf826f99a63\\transformed\\material3-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,411,529,630,724,835,967,1083,1227,1311,1410,1506,1605,1730,1848,1952,2091,2226,2365,2561,2691,2809,2935,3062,3159,3260,3382,3511,3609,3712,3819,3957,4105,4214,4318,4402,4498,4594,4682,4772,4883,4963,5050,5150,5259,5355,5454,5542,5653,5749,5849,5987,6071,6174", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "169,289,406,524,625,719,830,962,1078,1222,1306,1405,1501,1600,1725,1843,1947,2086,2221,2360,2556,2686,2804,2930,3057,3154,3255,3377,3506,3604,3707,3814,3952,4100,4209,4313,4397,4493,4589,4677,4767,4878,4958,5045,5145,5254,5350,5449,5537,5648,5744,5844,5982,6066,6169,6266"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3765,3884,4004,4121,4239,4340,4434,4545,4677,4793,4937,5021,5120,5216,5315,5440,5558,5662,5801,5936,6075,6271,6401,6519,6645,6772,6869,6970,7092,7221,7319,7422,7529,7667,7815,7924,8028,8112,8208,8304,8392,8482,8593,8673,8760,8860,8969,9065,9164,9252,9363,9459,9559,9697,9781,9884", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "3879,3999,4116,4234,4335,4429,4540,4672,4788,4932,5016,5115,5211,5310,5435,5553,5657,5796,5931,6070,6266,6396,6514,6640,6767,6864,6965,7087,7216,7314,7417,7524,7662,7810,7919,8023,8107,8203,8299,8387,8477,8588,8668,8755,8855,8964,9060,9159,9247,9358,9454,9554,9692,9776,9879,9976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcab984314392b621ac00752db419e20\\transformed\\foundation-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "102,103", "startColumns": "4,4", "startOffsets": "11016,11104", "endColumns": "87,87", "endOffsets": "11099,11187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77ea1b825b369621456509c77bf3bf13\\transformed\\play-services-basement-18.2.0\\res\\values-lt\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "154", "endOffsets": "349"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2039", "endColumns": "158", "endOffsets": "2193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed67bf69c499c3dc23679679d1ab859d\\transformed\\ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,1005,1093,1168,1245,1322,1397,1477,1560", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,1000,1088,1163,1240,1317,1392,1472,1555,1677"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,97,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "856,949,3299,3397,3502,3597,3674,9981,10068,10152,10238,10326,10401,10478,10555,10731,10811,10894", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "944,1028,3392,3497,3592,3669,3760,10063,10147,10233,10321,10396,10473,10550,10625,10806,10889,11011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\508400dcba8679bb0e6e26d4d4e1ebd2\\transformed\\play-services-base-18.0.1\\res\\values-lt\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,444,572,675,824,950,1065,1167,1329,1434,1595,1725,1874,2020,2084,2146", "endColumns": "102,147,127,102,148,125,114,101,161,104,160,129,148,145,63,61,85", "endOffsets": "295,443,571,674,823,949,1064,1166,1328,1433,1594,1724,1873,2019,2083,2145,2231"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1033,1140,1292,1424,1531,1684,1814,1933,2198,2364,2473,2638,2772,2925,3075,3143,3209", "endColumns": "106,151,131,106,152,129,118,105,165,108,164,133,152,149,67,65,89", "endOffsets": "1135,1287,1419,1526,1679,1809,1928,2034,2359,2468,2633,2767,2920,3070,3138,3204,3294"}}]}]}