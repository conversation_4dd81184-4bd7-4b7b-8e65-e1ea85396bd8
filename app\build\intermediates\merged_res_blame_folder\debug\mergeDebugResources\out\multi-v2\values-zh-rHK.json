{"logs": [{"outputFile": "com.example.otpautoread.app-mergeDebugResources-54:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcab984314392b621ac00752db419e20\\transformed\\foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "102,103", "startColumns": "4,4", "startOffsets": "9516,9597", "endColumns": "80,76", "endOffsets": "9592,9669"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77ea1b825b369621456509c77bf3bf13\\transformed\\play-services-basement-18.2.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1780", "endColumns": "107", "endOffsets": "1883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\508400dcba8679bb0e6e26d4d4e1ebd2\\transformed\\play-services-base-18.0.1\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "916,1017,1145,1260,1362,1469,1585,1687,1888,1998,2099,2228,2343,2450,2558,2613,2670", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "1012,1140,1255,1357,1464,1580,1682,1775,1993,2094,2223,2338,2445,2553,2608,2665,2737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7d7640ada57095f48ab28f83984208c\\transformed\\core-1.16.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "2,3,4,5,6,7,8,98", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,296,390,484,577,670,9159", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "192,291,385,479,572,665,761,9255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0dcbd671948b5a784f1cdbf826f99a63\\transformed\\material3-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,364,465,556,645,750,855,960,1076,1158,1254,1338,1426,1531,1644,1745,1853,1959,2067,2183,2288,2390,2495,2601,2686,2781,2886,2995,3085,3187,3285,3394,3508,3608,3699,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4924,5020,5115,5223,5303,5395", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "153,255,359,460,551,640,745,850,955,1071,1153,1249,1333,1421,1526,1639,1740,1848,1954,2062,2178,2283,2385,2490,2596,2681,2776,2881,2990,3080,3182,3280,3389,3503,3603,3694,3767,3857,3946,4029,4111,4200,4280,4362,4459,4553,4646,4739,4823,4919,5015,5110,5218,5298,5390,5480"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3150,3253,3355,3459,3560,3651,3740,3845,3950,4055,4171,4253,4349,4433,4521,4626,4739,4840,4948,5054,5162,5278,5383,5485,5590,5696,5781,5876,5981,6090,6180,6282,6380,6489,6603,6703,6794,6867,6957,7046,7129,7211,7300,7380,7462,7559,7653,7746,7839,7923,8019,8115,8210,8318,8398,8490", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "3248,3350,3454,3555,3646,3735,3840,3945,4050,4166,4248,4344,4428,4516,4621,4734,4835,4943,5049,5157,5273,5378,5480,5585,5691,5776,5871,5976,6085,6175,6277,6375,6484,6598,6698,6789,6862,6952,7041,7124,7206,7295,7375,7457,7554,7648,7741,7834,7918,8014,8110,8205,8313,8393,8485,8575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed67bf69c499c3dc23679679d1ab859d\\transformed\\ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,888,963,1030,1103,1173,1242,1317,1382", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,72,69,68,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,883,958,1025,1098,1168,1237,1312,1377,1493"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,97,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,842,2742,2830,2921,2999,3073,8580,8658,8732,8805,8880,8947,9020,9090,9260,9335,9400", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,72,69,68,74,64,115", "endOffsets": "837,911,2825,2916,2994,3068,3145,8653,8727,8800,8875,8942,9015,9085,9154,9330,9395,9511"}}]}]}