{"logs": [{"outputFile": "com.example.otpautoread.app-mergeDebugResources-53:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0dcbd671948b5a784f1cdbf826f99a63\\transformed\\material3-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,401,515,612,710,825,958,1067,1209,1293,1397,1491,1589,1703,1824,1933,2058,2181,2311,2479,2604,2725,2849,2970,3065,3163,3280,3406,3510,3620,3727,3850,3978,4091,4195,4279,4375,4469,4557,4643,4744,4824,4908,5008,5112,5208,5307,5395,5503,5603,5706,5845,5925,6041", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "167,282,396,510,607,705,820,953,1062,1204,1288,1392,1486,1584,1698,1819,1928,2053,2176,2306,2474,2599,2720,2844,2965,3060,3158,3275,3401,3505,3615,3722,3845,3973,4086,4190,4274,4370,4464,4552,4638,4739,4819,4903,5003,5107,5203,5302,5390,5498,5598,5701,5840,5920,6036,6139"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3746,3863,3978,4092,4206,4303,4401,4516,4649,4758,4900,4984,5088,5182,5280,5394,5515,5624,5749,5872,6002,6170,6295,6416,6540,6661,6756,6854,6971,7097,7201,7311,7418,7541,7669,7782,7886,7970,8066,8160,8248,8334,8435,8515,8599,8699,8803,8899,8998,9086,9194,9294,9397,9536,9616,9732", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "3858,3973,4087,4201,4298,4396,4511,4644,4753,4895,4979,5083,5177,5275,5389,5510,5619,5744,5867,5997,6165,6290,6411,6535,6656,6751,6849,6966,7092,7196,7306,7413,7536,7664,7777,7881,7965,8061,8155,8243,8329,8430,8510,8594,8694,8798,8894,8993,9081,9189,9289,9392,9531,9611,9727,9830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77ea1b825b369621456509c77bf3bf13\\transformed\\play-services-basement-18.2.0\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2022", "endColumns": "149", "endOffsets": "2167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7d7640ada57095f48ab28f83984208c\\transformed\\core-1.16.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "2,3,4,5,6,7,8,98", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,309,410,510,618,722,10473", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "202,304,405,505,613,717,836,10569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\508400dcba8679bb0e6e26d4d4e1ebd2\\transformed\\play-services-base-18.0.1\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1025,1130,1280,1409,1518,1663,1796,1916,2172,2344,2452,2611,2743,2897,3059,3125,3186", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "1125,1275,1404,1513,1658,1791,1911,2017,2339,2447,2606,2738,2892,3054,3120,3181,3261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcab984314392b621ac00752db419e20\\transformed\\foundation-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,100", "endOffsets": "161,262"}, "to": {"startLines": "102,103", "startColumns": "4,4", "startOffsets": "10842,10953", "endColumns": "110,100", "endOffsets": "10948,11049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed67bf69c499c3dc23679679d1ab859d\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1026,1109,1183,1259,1334,1407,1490,1558", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,75,74,72,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1021,1104,1178,1254,1329,1402,1485,1553,1670"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,97,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "841,940,3266,3370,3477,3573,3656,9835,9928,10011,10092,10175,10249,10325,10400,10574,10657,10725", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,75,74,72,82,67,116", "endOffsets": "935,1020,3365,3472,3568,3651,3741,9923,10006,10087,10170,10244,10320,10395,10468,10652,10720,10837"}}]}]}