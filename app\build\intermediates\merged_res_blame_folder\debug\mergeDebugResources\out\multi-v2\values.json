{"logs": [{"outputFile": "com.example.otpautoread.app-mergeDebugResources-54:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcab984314392b621ac00752db419e20\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "227,228", "startColumns": "4,4", "startOffsets": "15837,15893", "endColumns": "55,54", "endOffsets": "15888,15943"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\OTPAutoRead\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "5,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,1346,1393,1440,1487,1532,1577", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,1388,1435,1482,1527,1572,1614"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e72c1982aa78dbcf7a04fa40e7da3349\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "84,93,117,325,330", "startColumns": "4,4,4,4,4", "startOffsets": "5395,5857,7145,20342,20512", "endLines": "84,93,117,329,333", "endColumns": "56,64,63,24,24", "endOffsets": "5447,5917,7204,20507,20656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7214933b3732777e4b08eec927b7ae8\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "121", "startColumns": "4", "startOffsets": "7400", "endColumns": "82", "endOffsets": "7478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\679d72a393a232579b38fa6bafa145be\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "88,91", "startColumns": "4,4", "startOffsets": "5624,5748", "endColumns": "53,66", "endOffsets": "5673,5810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dbf1ee37e262d442933943ac7ae703ee\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "6938", "endColumns": "42", "endOffsets": "6976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\38590eb04d46a9ab96535545831f3954\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "7041", "endColumns": "53", "endOffsets": "7090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\293c5a463a7150379d5e0c3058be757e\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "85", "startColumns": "4", "startOffsets": "5452", "endColumns": "65", "endOffsets": "5513"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\OTPAutoRead\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "88", "endOffsets": "140"}, "to": {"startLines": "250", "startColumns": "4", "startOffsets": "17124", "endColumns": "87", "endOffsets": "17207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a2ba54a281b3bc6aa123e2e5e7e72328\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "112", "startColumns": "4", "startOffsets": "6895", "endColumns": "42", "endOffsets": "6933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4b9eafa61a03d81530af2183717f5d4a\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "92,114", "startColumns": "4,4", "startOffsets": "5815,6981", "endColumns": "41,59", "endOffsets": "5852,7036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0dcbd671948b5a784f1cdbf826f99a63\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "155,156,157,158,159,160,161,162,163,164,167,168,169,170,171,172,173,174,175,176,177,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10832,10920,11006,11087,11171,11240,11305,11388,11494,11580,11700,11754,11823,11884,11953,12042,12137,12211,12308,12401,12499,12648,12739,12827,12923,13021,13085,13153,13240,13334,13401,13473,13545,13646,13755,13831,13900,13948,14014,14078,14135,14192,14264,14314,14368,14439,14510,14580,14649,14707,14783,14854,14928,15014,15064,15134", "endLines": "155,156,157,158,159,160,161,162,163,166,167,168,169,170,171,172,173,174,175,176,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "10915,11001,11082,11166,11235,11300,11383,11489,11575,11695,11749,11818,11879,11948,12037,12132,12206,12303,12396,12494,12643,12734,12822,12918,13016,13080,13148,13235,13329,13396,13468,13540,13641,13750,13826,13895,13943,14009,14073,14130,14187,14259,14309,14363,14434,14505,14575,14644,14702,14778,14849,14923,15009,15059,15129,15194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7d7640ada57095f48ab28f83984208c\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "2,3,4,6,7,16,17,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,120,123,124,125,126,127,128,129,223,239,240,244,245,249,251,252,253,259,269,304,334,367", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,1214,1283,1619,1689,1757,1829,1899,1960,2034,2107,2168,2229,2291,2355,2417,2478,2546,2646,2706,2772,2845,2914,2971,3023,3085,3157,3233,3298,3357,3416,3476,3536,3596,3656,3716,3776,3836,3896,3956,4016,4075,4135,4195,4255,4315,4375,4435,4495,4555,4615,4675,4734,4794,4854,4913,4972,5031,5090,5149,5678,5713,5922,5977,6040,6095,6153,6209,6267,6328,6391,6448,6499,6557,6607,6668,6725,6791,6825,6860,7330,7532,7599,7671,7740,7809,7883,7955,15593,16366,16483,16684,16794,16995,17212,17284,17351,17554,17855,19661,20661,21343", "endLines": "2,3,4,6,7,16,17,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,120,123,124,125,126,127,128,129,223,239,243,244,248,249,251,252,258,268,303,324,366,372", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,472,538,1278,1341,1684,1752,1824,1894,1955,2029,2102,2163,2224,2286,2350,2412,2473,2541,2641,2701,2767,2840,2909,2966,3018,3080,3152,3228,3293,3352,3411,3471,3531,3591,3651,3711,3771,3831,3891,3951,4011,4070,4130,4190,4250,4310,4370,4430,4490,4550,4610,4670,4729,4789,4849,4908,4967,5026,5085,5144,5203,5708,5743,5972,6035,6090,6148,6204,6262,6323,6386,6443,6494,6552,6602,6663,6720,6786,6820,6855,6890,7395,7594,7666,7735,7804,7878,7950,8038,15659,16478,16679,16789,16990,17119,17279,17346,17549,17850,19656,20337,21338,21505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed67bf69c499c3dc23679679d1ab859d\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "81,82,83,86,87,118,130,131,150,151,152,153,154,215,216,217,218,219,220,221,222,224,225,226,229,232,235", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5208,5282,5340,5518,5569,7209,8043,8108,10495,10561,10662,10720,10772,15199,15261,15315,15365,15419,15465,15511,15553,15664,15711,15747,15948,16060,16171", "endLines": "81,82,83,86,87,118,130,131,150,151,152,153,154,215,216,217,218,219,220,221,222,224,225,226,231,234,238", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "5277,5335,5390,5564,5619,7257,8103,8157,10556,10657,10715,10767,10827,15256,15310,15360,15414,15460,15506,15548,15588,15706,15742,15832,16055,16166,16361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77ea1b825b369621456509c77bf3bf13\\transformed\\play-services-basement-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "119,140", "startColumns": "4,4", "startOffsets": "7262,9202", "endColumns": "67,166", "endOffsets": "7325,9364"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\OTPAutoRead\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "48", "endOffsets": "60"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "7483", "endColumns": "48", "endOffsets": "7527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75224fa514fbb5ee80caa4a4f816c7bd\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "7095", "endColumns": "49", "endOffsets": "7140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\508400dcba8679bb0e6e26d4d4e1ebd2\\transformed\\play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "8,9,10,11,12,13,14,15,132,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,149,373,386", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "543,633,713,803,893,973,1054,1134,8162,8267,8448,8573,8680,8860,8983,9099,9369,9557,9662,9843,9968,10143,10291,10354,10416,21510,21825", "endLines": "8,9,10,11,12,13,14,15,132,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,149,385,404", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "628,708,798,888,968,1049,1129,1209,8262,8443,8568,8675,8855,8978,9094,9197,9552,9657,9838,9963,10138,10286,10349,10411,10490,21820,22237"}}]}]}