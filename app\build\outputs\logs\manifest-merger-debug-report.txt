-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:2:1-40:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:2:1-40:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:2:1-40:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:2:1-40:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cce6651a6090c50645f2230fe0e03845\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\993d3a5bd91f00a8a88f0898235f2ee3\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87bdc3a20491779900280870287a0b93\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53b3e791e688cd9a14f376d658d58c11\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e72c1982aa78dbcf7a04fa40e7da3349\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5735cad7b276c6d97df991acbe30c03f\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6dd481fbb3c51a99fdff95b547e77b3\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb9f2140eba6045607340b99c1236e2b\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bde393c08d62ac9d50a0692032dfcf0c\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dcbd671948b5a784f1cdbf826f99a63\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44eb68f6ef1a83bafd08ca6c076b4ff3\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c4a1797a7d446b9e6407510b1188b63\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd76f055794aa76b6f3abe7c993956c4\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0dafbadec56a23a458289768cc4961\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f2ea8eb828408b02342aa4399876273\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21523d68aefe356d4c54e28fe2d8c92d\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0d28f9ce7fb2c81a3e9d5ce0606e4e9\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6270c125ad470f6421a3f72eb7b05198\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcab984314392b621ac00752db419e20\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9ca4b3cbb2645d65fc84c770e332f2\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0e93d386ab89167e114162eb6edad6\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ead78bf3bf0f87f578cd56601930c5f\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0976f4ef6139dbd92ec64cebbfe48fa1\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\293c5a463a7150379d5e0c3058be757e\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63353f9cd975c7a5dc0841d63510648d\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61356cc6e2d5d63fd67ba041c1e3d78a\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8501478d84d5142d4f0a57800cae5ca\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82c90d13be75fa1c9a4f5d49250a6521\transformed\runtime-livedata-1.7.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7edd56ab8cd346e58ff0c4ea3be75bb2\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd6c4af7859420983cfa88b2dca12238\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65a445db41be92b095d39fcee3670702\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75224fa514fbb5ee80caa4a4f816c7bd\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89f03305be7de942d4c52ae566f6c177\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38590eb04d46a9ab96535545831f3954\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbf1ee37e262d442933943ac7ae703ee\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65b8cc3292c1ac362957c4b464f2b06e\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9f2169c6832401b94f7f05249d841f0\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f1dc23efcfbbd6e6d48aae1cf2045f3\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff407903b466c719d84f14f2cc338baa\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc23b0fe9a01f8cb40e302d665e2aff4\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9214656bbea4cf5878549cc0b7fe026\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\788fa4a711e1df3d46f2712b9b7e088f\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed67bf69c499c3dc23679679d1ab859d\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b9eafa61a03d81530af2183717f5d4a\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b0a8727cb5e59350df8a3f51b15ccb8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4218b28c0dd2616759dbce77817b2e0a\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\679d72a393a232579b38fa6bafa145be\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d436fc9d3909122b736b2e84d77eb69\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49604f323c7479d1187ffedc83abcea8\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2ba54a281b3bc6aa123e2e5e7e72328\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcbf38f1075ffd56f12f7b4c5b6f91fc\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1ea19313087b6ce8b06ea1859230f7c\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7214933b3732777e4b08eec927b7ae8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14f99a42016928941fe31307fe3f85f8\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\406e202abe1a8f3f3b3ef7a98655d46d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#com.google.android.gms.permission.SMS_RETRIEVER
ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:6:5-87
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:6:22-84
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:8:5-38:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:8:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87bdc3a20491779900280870287a0b93\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87bdc3a20491779900280870287a0b93\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53b3e791e688cd9a14f376d658d58c11\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53b3e791e688cd9a14f376d658d58c11\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcbf38f1075ffd56f12f7b4c5b6f91fc\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcbf38f1075ffd56f12f7b4c5b6f91fc\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7214933b3732777e4b08eec927b7ae8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7214933b3732777e4b08eec927b7ae8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:15:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:13:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:11:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:14:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:17:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:12:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:9:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:16:9-49
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:10:9-65
activity#com.example.otpautoread.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:18:9-28:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:21:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:20:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:22:13-53
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:19:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:23:13-27:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:24:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:24:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:26:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:26:27-74
receiver#com.example.otpautoread.sms.SMSBroadcastReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:31:9-37:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:33:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:32:13-53
intent-filter#action:name:com.google.android.gms.auth.api.phone.SMS_RETRIEVED
ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:34:13-36:29
action#com.google.android.gms.auth.api.phone.SMS_RETRIEVED
ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:35:17-94
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:35:25-91
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cce6651a6090c50645f2230fe0e03845\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cce6651a6090c50645f2230fe0e03845\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\993d3a5bd91f00a8a88f0898235f2ee3\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\993d3a5bd91f00a8a88f0898235f2ee3\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87bdc3a20491779900280870287a0b93\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87bdc3a20491779900280870287a0b93\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53b3e791e688cd9a14f376d658d58c11\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53b3e791e688cd9a14f376d658d58c11\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e72c1982aa78dbcf7a04fa40e7da3349\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e72c1982aa78dbcf7a04fa40e7da3349\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5735cad7b276c6d97df991acbe30c03f\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5735cad7b276c6d97df991acbe30c03f\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6dd481fbb3c51a99fdff95b547e77b3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6dd481fbb3c51a99fdff95b547e77b3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb9f2140eba6045607340b99c1236e2b\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb9f2140eba6045607340b99c1236e2b\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bde393c08d62ac9d50a0692032dfcf0c\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bde393c08d62ac9d50a0692032dfcf0c\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dcbd671948b5a784f1cdbf826f99a63\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dcbd671948b5a784f1cdbf826f99a63\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44eb68f6ef1a83bafd08ca6c076b4ff3\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44eb68f6ef1a83bafd08ca6c076b4ff3\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c4a1797a7d446b9e6407510b1188b63\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c4a1797a7d446b9e6407510b1188b63\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd76f055794aa76b6f3abe7c993956c4\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd76f055794aa76b6f3abe7c993956c4\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0dafbadec56a23a458289768cc4961\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0dafbadec56a23a458289768cc4961\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f2ea8eb828408b02342aa4399876273\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f2ea8eb828408b02342aa4399876273\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21523d68aefe356d4c54e28fe2d8c92d\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21523d68aefe356d4c54e28fe2d8c92d\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0d28f9ce7fb2c81a3e9d5ce0606e4e9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0d28f9ce7fb2c81a3e9d5ce0606e4e9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6270c125ad470f6421a3f72eb7b05198\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6270c125ad470f6421a3f72eb7b05198\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcab984314392b621ac00752db419e20\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcab984314392b621ac00752db419e20\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9ca4b3cbb2645d65fc84c770e332f2\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d9ca4b3cbb2645d65fc84c770e332f2\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0e93d386ab89167e114162eb6edad6\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0e93d386ab89167e114162eb6edad6\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ead78bf3bf0f87f578cd56601930c5f\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ead78bf3bf0f87f578cd56601930c5f\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0976f4ef6139dbd92ec64cebbfe48fa1\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0976f4ef6139dbd92ec64cebbfe48fa1\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\293c5a463a7150379d5e0c3058be757e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\293c5a463a7150379d5e0c3058be757e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63353f9cd975c7a5dc0841d63510648d\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63353f9cd975c7a5dc0841d63510648d\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61356cc6e2d5d63fd67ba041c1e3d78a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61356cc6e2d5d63fd67ba041c1e3d78a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8501478d84d5142d4f0a57800cae5ca\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8501478d84d5142d4f0a57800cae5ca\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82c90d13be75fa1c9a4f5d49250a6521\transformed\runtime-livedata-1.7.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82c90d13be75fa1c9a4f5d49250a6521\transformed\runtime-livedata-1.7.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7edd56ab8cd346e58ff0c4ea3be75bb2\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7edd56ab8cd346e58ff0c4ea3be75bb2\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd6c4af7859420983cfa88b2dca12238\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd6c4af7859420983cfa88b2dca12238\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65a445db41be92b095d39fcee3670702\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65a445db41be92b095d39fcee3670702\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75224fa514fbb5ee80caa4a4f816c7bd\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75224fa514fbb5ee80caa4a4f816c7bd\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89f03305be7de942d4c52ae566f6c177\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\89f03305be7de942d4c52ae566f6c177\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38590eb04d46a9ab96535545831f3954\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38590eb04d46a9ab96535545831f3954\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbf1ee37e262d442933943ac7ae703ee\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbf1ee37e262d442933943ac7ae703ee\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65b8cc3292c1ac362957c4b464f2b06e\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\65b8cc3292c1ac362957c4b464f2b06e\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9f2169c6832401b94f7f05249d841f0\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d9f2169c6832401b94f7f05249d841f0\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f1dc23efcfbbd6e6d48aae1cf2045f3\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f1dc23efcfbbd6e6d48aae1cf2045f3\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff407903b466c719d84f14f2cc338baa\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff407903b466c719d84f14f2cc338baa\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc23b0fe9a01f8cb40e302d665e2aff4\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc23b0fe9a01f8cb40e302d665e2aff4\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9214656bbea4cf5878549cc0b7fe026\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9214656bbea4cf5878549cc0b7fe026\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\788fa4a711e1df3d46f2712b9b7e088f\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\788fa4a711e1df3d46f2712b9b7e088f\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed67bf69c499c3dc23679679d1ab859d\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed67bf69c499c3dc23679679d1ab859d\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b9eafa61a03d81530af2183717f5d4a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b9eafa61a03d81530af2183717f5d4a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b0a8727cb5e59350df8a3f51b15ccb8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b0a8727cb5e59350df8a3f51b15ccb8\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4218b28c0dd2616759dbce77817b2e0a\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4218b28c0dd2616759dbce77817b2e0a\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\679d72a393a232579b38fa6bafa145be\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\679d72a393a232579b38fa6bafa145be\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d436fc9d3909122b736b2e84d77eb69\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d436fc9d3909122b736b2e84d77eb69\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49604f323c7479d1187ffedc83abcea8\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\49604f323c7479d1187ffedc83abcea8\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2ba54a281b3bc6aa123e2e5e7e72328\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2ba54a281b3bc6aa123e2e5e7e72328\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcbf38f1075ffd56f12f7b4c5b6f91fc\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcbf38f1075ffd56f12f7b4c5b6f91fc\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1ea19313087b6ce8b06ea1859230f7c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1ea19313087b6ce8b06ea1859230f7c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7214933b3732777e4b08eec927b7ae8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7214933b3732777e4b08eec927b7ae8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14f99a42016928941fe31307fe3f85f8\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\14f99a42016928941fe31307fe3f85f8\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\406e202abe1a8f3f3b3ef7a98655d46d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\406e202abe1a8f3f3b3ef7a98655d46d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7214933b3732777e4b08eec927b7ae8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7214933b3732777e4b08eec927b7ae8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.otpautoread.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.otpautoread.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
