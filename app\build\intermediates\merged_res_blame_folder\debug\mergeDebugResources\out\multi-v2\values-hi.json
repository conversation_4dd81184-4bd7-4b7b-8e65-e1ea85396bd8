{"logs": [{"outputFile": "com.example.otpautoread.app-mergeDebugResources-53:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed67bf69c499c3dc23679679d1ab859d\\transformed\\ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,993,1078,1151,1244,1319,1394,1475,1541", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,988,1073,1146,1239,1314,1389,1470,1536,1656"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,97,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "858,953,3267,3360,3458,3547,3625,9909,9998,10083,10164,10249,10322,10415,10490,10666,10747,10813", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,92,74,74,80,65,119", "endOffsets": "948,1031,3355,3453,3542,3620,3717,9993,10078,10159,10244,10317,10410,10485,10560,10742,10808,10928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7d7640ada57095f48ab28f83984208c\\transformed\\core-1.16.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "2,3,4,5,6,7,8,98", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,411,512,625,731,10565", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "198,301,406,507,620,726,853,10661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77ea1b825b369621456509c77bf3bf13\\transformed\\play-services-basement-18.2.0\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2047", "endColumns": "145", "endOffsets": "2188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcab984314392b621ac00752db419e20\\transformed\\foundation-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,85", "endOffsets": "135,221"}, "to": {"startLines": "102,103", "startColumns": "4,4", "startOffsets": "10933,11018", "endColumns": "84,85", "endOffsets": "11013,11099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\508400dcba8679bb0e6e26d4d4e1ebd2\\transformed\\play-services-base-18.0.1\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1036,1144,1304,1430,1542,1693,1823,1935,2193,2350,2459,2625,2755,2896,3049,3112,3179", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "1139,1299,1425,1537,1688,1818,1930,2042,2345,2454,2620,2750,2891,3044,3107,3174,3262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0dcbd671948b5a784f1cdbf826f99a63\\transformed\\material3-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,415,531,626,722,835,973,1093,1243,1328,1431,1522,1619,1749,1869,1977,2122,2268,2398,2587,2714,2832,2954,3080,3172,3267,3395,3521,3620,3722,3834,3980,4132,4246,4346,4422,4522,4621,4707,4797,4902,4982,5066,5166,5266,5361,5463,5549,5651,5749,5853,5968,6048,6148", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "168,286,410,526,621,717,830,968,1088,1238,1323,1426,1517,1614,1744,1864,1972,2117,2263,2393,2582,2709,2827,2949,3075,3167,3262,3390,3516,3615,3717,3829,3975,4127,4241,4341,4417,4517,4616,4702,4792,4897,4977,5061,5161,5261,5356,5458,5544,5646,5744,5848,5963,6043,6143,6237"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3722,3840,3958,4082,4198,4293,4389,4502,4640,4760,4910,4995,5098,5189,5286,5416,5536,5644,5789,5935,6065,6254,6381,6499,6621,6747,6839,6934,7062,7188,7287,7389,7501,7647,7799,7913,8013,8089,8189,8288,8374,8464,8569,8649,8733,8833,8933,9028,9130,9216,9318,9416,9520,9635,9715,9815", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "3835,3953,4077,4193,4288,4384,4497,4635,4755,4905,4990,5093,5184,5281,5411,5531,5639,5784,5930,6060,6249,6376,6494,6616,6742,6834,6929,7057,7183,7282,7384,7496,7642,7794,7908,8008,8084,8184,8283,8369,8459,8564,8644,8728,8828,8928,9023,9125,9211,9313,9411,9515,9630,9710,9810,9904"}}]}]}