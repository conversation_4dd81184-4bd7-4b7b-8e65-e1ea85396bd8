1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.otpautoread"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permission for SMS Retriever API -->
12    <uses-permission android:name="com.google.android.gms.permission.SMS_RETRIEVER" />
12-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:6:5-87
12-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:6:22-84
13
14    <permission
14-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.otpautoread.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.otpautoread.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:8:5-38:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:15:9-35
31        android:testOnly="true"
32        android:theme="@style/Theme.OTPAutoRead" >
32-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:16:9-49
33        <activity
33-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:18:9-28:20
34            android:name="com.example.otpautoread.MainActivity"
34-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:19:13-41
35            android:exported="true"
35-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:20:13-36
36            android:label="@string/app_name"
36-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:21:13-45
37            android:theme="@style/Theme.OTPAutoRead" >
37-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:22:13-53
38            <intent-filter>
38-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:23:13-27:29
39                <action android:name="android.intent.action.MAIN" />
39-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:24:17-69
39-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:24:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:26:17-77
41-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:26:27-74
42            </intent-filter>
43        </activity>
44
45        <!-- SMS Broadcast Receiver for handling SMS Retriever API -->
46        <receiver
46-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:31:9-37:20
47            android:name="com.example.otpautoread.sms.SMSBroadcastReceiver"
47-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:32:13-53
48            android:exported="false" >
48-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:33:13-37
49            <intent-filter>
49-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:34:13-36:29
50                <action android:name="com.google.android.gms.auth.api.phone.SMS_RETRIEVED" />
50-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:35:17-94
50-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:35:25-91
51            </intent-filter>
52        </receiver>
53
54        <activity
54-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
55            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
55-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
56            android:excludeFromRecents="true"
56-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
57            android:exported="false"
57-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
58            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
58-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
59        <!--
60            Service handling Google Sign-In user revocation. For apps that do not integrate with
61            Google Sign-In, this service will never be started.
62        -->
63        <service
63-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
64            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
64-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
65            android:exported="true"
65-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
66            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
66-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
67            android:visibleToInstantApps="true" />
67-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
68
69        <activity
69-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
70            android:name="com.google.android.gms.common.api.GoogleApiActivity"
70-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
71            android:exported="false"
71-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
72            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
72-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
73
74        <meta-data
74-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
75            android:name="com.google.android.gms.version"
75-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
76            android:value="@integer/google_play_services_version" />
76-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
77
78        <activity
78-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
79            android:name="androidx.compose.ui.tooling.PreviewActivity"
79-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
80            android:exported="true" />
80-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
81
82        <provider
82-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
83            android:name="androidx.startup.InitializationProvider"
83-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
84            android:authorities="com.example.otpautoread.androidx-startup"
84-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
85            android:exported="false" >
85-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
86            <meta-data
86-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
87                android:name="androidx.emoji2.text.EmojiCompatInitializer"
87-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
88                android:value="androidx.startup" />
88-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
89            <meta-data
89-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
90                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
90-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
91                android:value="androidx.startup" />
91-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
92            <meta-data
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
93                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
94                android:value="androidx.startup" />
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
95        </provider>
96
97        <activity
97-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
98            android:name="androidx.activity.ComponentActivity"
98-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
99            android:exported="true" />
99-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
100
101        <receiver
101-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
102            android:name="androidx.profileinstaller.ProfileInstallReceiver"
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
103            android:directBootAware="false"
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
104            android:enabled="true"
104-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
105            android:exported="true"
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
106            android:permission="android.permission.DUMP" >
106-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
108                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
108-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
108-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
111                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
111-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
111-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
114                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
115            </intent-filter>
116            <intent-filter>
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
117                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
117-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
117-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
118            </intent-filter>
119        </receiver>
120    </application>
121
122</manifest>
