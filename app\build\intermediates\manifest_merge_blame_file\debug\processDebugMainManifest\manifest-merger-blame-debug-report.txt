1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.otpautoread"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permission for SMS Retriever API -->
12    <uses-permission android:name="com.google.android.gms.permission.SMS_RETRIEVER" />
12-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:6:5-87
12-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:6:22-84
13
14    <permission
14-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.otpautoread.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.otpautoread.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:8:5-38:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7d7640ada57095f48ab28f83984208c\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:15:9-35
31        android:theme="@style/Theme.OTPAutoRead" >
31-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:16:9-49
32        <activity
32-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:18:9-28:20
33            android:name="com.example.otpautoread.MainActivity"
33-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:19:13-41
34            android:exported="true"
34-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:20:13-36
35            android:label="@string/app_name"
35-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:21:13-45
36            android:theme="@style/Theme.OTPAutoRead" >
36-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:22:13-53
37            <intent-filter>
37-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:23:13-27:29
38                <action android:name="android.intent.action.MAIN" />
38-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:24:17-69
38-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:24:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:26:17-77
40-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:26:27-74
41            </intent-filter>
42        </activity>
43
44        <!-- SMS Broadcast Receiver for handling SMS Retriever API -->
45        <receiver
45-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:31:9-37:20
46            android:name="com.example.otpautoread.sms.SMSBroadcastReceiver"
46-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:32:13-53
47            android:exported="false" >
47-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:33:13-37
48            <intent-filter>
48-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:34:13-36:29
49                <action android:name="com.google.android.gms.auth.api.phone.SMS_RETRIEVED" />
49-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:35:17-94
49-->C:\Users\<USER>\AndroidStudioProjects\OTPAutoRead\app\src\main\AndroidManifest.xml:35:25-91
50            </intent-filter>
51        </receiver>
52
53        <activity
53-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
54            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
54-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
55            android:excludeFromRecents="true"
55-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
56            android:exported="false"
56-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
57            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
57-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
58        <!--
59            Service handling Google Sign-In user revocation. For apps that do not integrate with
60            Google Sign-In, this service will never be started.
61        -->
62        <service
62-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
63            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
63-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
64            android:exported="true"
64-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
65            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
65-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
66            android:visibleToInstantApps="true" />
66-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7e9da094f56f261f27eeca0d2fc6a02\transformed\play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
67
68        <activity
68-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
69            android:name="com.google.android.gms.common.api.GoogleApiActivity"
69-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
70            android:exported="false"
70-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
71            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
71-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\508400dcba8679bb0e6e26d4d4e1ebd2\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
72
73        <meta-data
73-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
74            android:name="com.google.android.gms.version"
74-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
75            android:value="@integer/google_play_services_version" />
75-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ea1b825b369621456509c77bf3bf13\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
76
77        <activity
77-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
78            android:name="androidx.compose.ui.tooling.PreviewActivity"
78-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
79            android:exported="true" />
79-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f67077f65b368a62c697617381c61e\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
80
81        <provider
81-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
82            android:name="androidx.startup.InitializationProvider"
82-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
83            android:authorities="com.example.otpautoread.androidx-startup"
83-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
84            android:exported="false" >
84-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
85            <meta-data
85-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
86                android:name="androidx.emoji2.text.EmojiCompatInitializer"
86-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
87                android:value="androidx.startup" />
87-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5b9b88f57debe3ffa039d02fcf546f1\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
88            <meta-data
88-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
89-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
90                android:value="androidx.startup" />
90-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af595c74c7a060d954daf3e24923ae60\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
91            <meta-data
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
92                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
93                android:value="androidx.startup" />
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
94        </provider>
95
96        <activity
96-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
97            android:name="androidx.activity.ComponentActivity"
97-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
98            android:exported="true" />
98-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97115aabca006f5c78be185717d673be\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
99
100        <receiver
100-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
101            android:name="androidx.profileinstaller.ProfileInstallReceiver"
101-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
102            android:directBootAware="false"
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
103            android:enabled="true"
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
104            android:exported="true"
104-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
105            android:permission="android.permission.DUMP" >
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
107                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
110                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
113                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
116                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06fb803bc4933ac6decf2fcde2fd28e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
117            </intent-filter>
118        </receiver>
119    </application>
120
121</manifest>
