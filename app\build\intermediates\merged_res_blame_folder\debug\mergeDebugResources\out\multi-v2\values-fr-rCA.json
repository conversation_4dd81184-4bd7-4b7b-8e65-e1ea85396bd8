{"logs": [{"outputFile": "com.example.otpautoread.app-mergeDebugResources-53:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0dcbd671948b5a784f1cdbf826f99a63\\transformed\\material3-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,347,469,617,743,837,949,1091,1210,1369,1453,1554,1655,1756,1877,2012,2118,2268,2414,2550,2752,2881,2999,3122,3255,3357,3462,3586,3714,3816,3928,4033,4178,4330,4439,4548,4626,4719,4814,4904,4990,5097,5177,5262,5359,5470,5563,5667,5755,5871,5972,6081,6203,6283,6393", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "197,342,464,612,738,832,944,1086,1205,1364,1448,1549,1650,1751,1872,2007,2113,2263,2409,2545,2747,2876,2994,3117,3250,3352,3457,3581,3709,3811,3923,4028,4173,4325,4434,4543,4621,4714,4809,4899,4985,5092,5172,5257,5354,5465,5558,5662,5750,5866,5967,6076,6198,6278,6388,6485"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3892,4039,4184,4306,4454,4580,4674,4786,4928,5047,5206,5290,5391,5492,5593,5714,5849,5955,6105,6251,6387,6589,6718,6836,6959,7092,7194,7299,7423,7551,7653,7765,7870,8015,8167,8276,8385,8463,8556,8651,8741,8827,8934,9014,9099,9196,9307,9400,9504,9592,9708,9809,9918,10040,10120,10230", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "4034,4179,4301,4449,4575,4669,4781,4923,5042,5201,5285,5386,5487,5588,5709,5844,5950,6100,6246,6382,6584,6713,6831,6954,7087,7189,7294,7418,7546,7648,7760,7865,8010,8162,8271,8380,8458,8551,8646,8736,8822,8929,9009,9094,9191,9302,9395,9499,9587,9703,9804,9913,10035,10115,10225,10322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcab984314392b621ac00752db419e20\\transformed\\foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "102,103", "startColumns": "4,4", "startOffsets": "11346,11434", "endColumns": "87,94", "endOffsets": "11429,11524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed67bf69c499c3dc23679679d1ab859d\\transformed\\ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1017,1102,1178,1253,1331,1405,1484,1553", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,74,77,73,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1012,1097,1173,1248,1326,1400,1479,1548,1670"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,97,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "828,927,3427,3525,3631,3718,3798,10327,10419,10506,10587,10672,10748,10823,10901,11076,11155,11224", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,74,77,73,78,68,121", "endOffsets": "922,1010,3520,3626,3713,3793,3887,10414,10501,10582,10667,10743,10818,10896,10970,11150,11219,11341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\508400dcba8679bb0e6e26d4d4e1ebd2\\transformed\\play-services-base-18.0.1\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2300", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,59,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2299,2379"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1015,1121,1301,1431,1540,1711,1844,1965,2239,2434,2546,2731,2867,3027,3206,3279,3343", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,63,83", "endOffsets": "1116,1296,1426,1535,1706,1839,1960,2073,2429,2541,2726,2862,3022,3201,3274,3338,3422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b7d7640ada57095f48ab28f83984208c\\transformed\\core-1.16.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,98", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,404,506,610,714,10975", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "198,300,399,501,605,709,823,11071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\77ea1b825b369621456509c77bf3bf13\\transformed\\play-services-basement-18.2.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2078", "endColumns": "160", "endOffsets": "2234"}}]}]}